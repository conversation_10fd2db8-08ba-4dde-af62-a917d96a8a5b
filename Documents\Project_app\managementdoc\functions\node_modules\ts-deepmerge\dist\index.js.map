{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,uBAAuB;AACvB,IAAM,QAAQ,GAAG,UAAC,GAAQ;IACxB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;QAC3C,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,EAAE;YAC/C,IAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAC7C,OAAO,SAAS,KAAK,MAAM,CAAC,SAAS,IAAI,SAAS,KAAK,IAAI,CAAC;SAC7D;QAED,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC;KAClE;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,IAAM,KAAK,GAAG;IACZ,iBAAa;SAAb,UAAa,EAAb,qBAAa,EAAb,IAAa;QAAb,4BAAa;;IAEb,OAAA,OAAO,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,OAAO;QAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC1B,MAAM,IAAI,SAAS,CACjB,iEAAiE,CAClE,CAAC;SACH;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC/B,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC3D,OAAO;aACR;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC7D,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW;oBACrC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAE,MAAM,CAAC,GAAG,CAAe,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACtE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC1D,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAY,EAAE,OAAO,CAAC,GAAG,CAAY,CAAC,CAAC;aACtE;iBAAM;gBACL,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;aAC5B;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,EAAE,CAAQ;AAxBb,CAwBa,CAAC;AAMhB,IAAM,cAAc,GAAa;IAC/B,WAAW,EAAE,IAAI;CAClB,CAAC;AAEF,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC;AAE/B,KAAK,CAAC,WAAW,GAAG,UAClB,OAA0B;IAC1B,iBAAa;SAAb,UAAa,EAAb,qBAAa,EAAb,IAAa;QAAb,gCAAa;;IAEb,KAAK,CAAC,OAAO,cACX,WAAW,EAAE,IAAI,IACd,OAAO,CACX,CAAC;IAEF,IAAM,MAAM,GAAG,KAAK,wCAAI,OAAO,UAAC,CAAC;IAEjC,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC;IAE/B,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,kBAAe,KAAK,CAAC"}